import os
import logging
import sqlite3
import mysql.connector
from mysql.connector.cursor import MySQLCursor, MySQLCursorDict
from urllib.parse import urlparse
from .cursors import CustomCursor, CustomDictCursor
from utils import DRYRUN_DIR, SQL_LOGS_DIR, TS


class BaseAdapter:
    def __init__(self, conn, dryrun=False, db_name=None):
        self.conn = conn
        self.dryrun = dryrun
        self.db_name = db_name

    def close(self):
        self.conn.close()

    def commit(self):
        self.conn.commit()

    @staticmethod
    def parse_db_name(dsn: str) -> str:
        parsed = urlparse(dsn)
        if parsed.path:
            return parsed.path.strip("/")
        return "unknown"

    @staticmethod
    def write_to_file(filename, sql, params):
        with open(filename, "a", encoding="utf-8") as f:
            if params:
                params = ["None" if p == "" or p is None else p for p in params]
                sql = sql % tuple(list(map(str, params)))
            f.write(sql.strip() + ";\n")

    def _write_dryrun(self, sql, params):
        os.makedirs(DRYRUN_DIR, exist_ok=True)
        fname = os.path.join(DRYRUN_DIR, f"dryrun_{self.db_name}_{TS}.sql")
        self.write_to_file(fname, sql, params)

    def _write_sql_log(self, sql, params):
        os.makedirs(SQL_LOGS_DIR, exist_ok=True)
        fname = os.path.join(SQL_LOGS_DIR, f"log_{self.db_name}_{TS}.sql")
        self.write_to_file(fname, sql, params)


class MySQLAdapter(BaseAdapter):
    @classmethod
    def connect(cls, dsn, dryrun=False):
        parsed = urlparse(dsn)
        try:
            conn = mysql.connector.connect(
                host=parsed.hostname,
                port=parsed.port or 3306,
                user=parsed.username,
                password=parsed.password,
                database=parsed.path.strip("/"),
            )
        except mysql.connector.Error as e:
            logging.error(f"Error connecting to MySQL \n {parsed} \n Error: {e}")
            raise
        return cls(conn, dryrun=dryrun, db_name=cls.parse_db_name(dsn))

    def get_cursor(self, dictionary=False):
        if dictionary:
            return MySQLCursorDict(self.conn)
        return MySQLCursor(self.conn)

    def execute(self, sql, params=None):
        if self.dryrun:
            self._write_dryrun(sql, params)
        else:
            cur = self.get_cursor()
            cur.execute(sql, params or ())
            cur.close()
            self._write_sql_log(sql, params)

    def executemany(self, sql, params_list):
        if self.dryrun:
            for params in params_list:
                self._write_dryrun(sql, params)
        else:
            cur = self.get_cursor()
            try:
                cur.executemany(sql, params_list)
                for params in params_list:
                    self._write_sql_log(sql, params)
            except Exception as e:
                cur.close()
                logging.error(f"Error executing SQL:\n {sql=} \n Exception: {e}")
            cur.close()


class SQLiteAdapter(BaseAdapter):
    @classmethod
    def connect(cls, dsn, dryrun=False):
        db_path = dsn.replace("sqlite:///", "")
        try:
            conn = sqlite3.connect(db_path)
        except sqlite3.Error as e:
            logging.error(f"Error connecting to SQLite ({db_path}): {e}")
            raise
        return cls(conn, dryrun=dryrun, db_name=cls.parse_db_name(dsn))

    def get_cursor(self, dictionary=False):
        if dictionary:
            self.conn.row_factory = sqlite3.Row
            return self.conn.cursor(factory=CustomDictCursor)
        self.conn.row_factory = None
        return self.conn.cursor(factory=CustomCursor)

    def execute(self, sql, params=None):
        log_sql = sql
        sql = sql.replace("%s", "?")
        if self.dryrun:
            self._write_dryrun(log_sql, params)
        else:
            cur = self.get_cursor()
            cur.execute(sql, params or ())
            cur.close()
            self._write_sql_log(log_sql, params)

    def executemany(self, sql, params_list):
        log_sql = sql
        sql = sql.replace("%s", "?")
        if self.dryrun:
            for params in params_list:
                self._write_dryrun(log_sql, params)
        else:
            cur = self.get_cursor()
            cur.executemany(sql, params_list)
            cur.close()
            for params in params_list:
                self._write_sql_log(log_sql, params)
