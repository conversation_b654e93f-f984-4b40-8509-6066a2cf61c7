import sqlite3
from typing import Any


class CustomCursor(sqlite3.Cursor):
    def execute(self, *args: Any, **kwargs: Any) -> "CustomCursor":
        sql = args[0]
        sql = sql.replace("%s", "?")
        args = (sql, *args[1:])
        super().execute(*args, **kwargs)
        return self

    def executemany(self, *args: Any, **kwargs: Any) -> "CustomCursor":
        sql = args[0]
        sql = sql.replace("%s", "?")
        args = (sql, *args[1:])
        super().executemany(*args, **kwargs)
        return self


class CustomDictCursor(CustomCursor):
    def fetchone(self):
        row = super().fetchone()
        return dict(row) if row else None

    def fetchall(self):
        rows = super().fetchall()
        return [dict(row) for row in rows]
