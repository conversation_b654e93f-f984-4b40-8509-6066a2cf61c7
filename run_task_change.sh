#!/bin/bash
export PATH=/usr/local/bin:/usr/bin:/bin

LOGFILE="/home/<USER>/run_task_change.log"

set -e

{
    echo "===== $(date '+%Y-%m-%d %H:%M:%S') Starting job ====="
    echo "Activating python venv..."
    
    source /home/<USER>/virtualenv/api_server/3.13/bin/activate

    echo "Venv activated"
    echo "Starting sync..."
    
    python3 /home/<USER>/api_server/almishop/main.py get_changes --dryrun

    echo "Sync done."
    echo "Deactivating venv..."
    
    deactivate
    
    echo "Venv deactivated"
    echo "===== $(date '+%Y-%m-%d %H:%M:%S') Job finished successfully ====="
} >> "$LOGFILE" 2>&1
