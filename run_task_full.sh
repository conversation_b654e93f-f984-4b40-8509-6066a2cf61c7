#!/bin/bash
export PATH=/usr/local/bin:/usr/bin:/bin

LOGFILE="/home/<USER>/run_task_full.log"
backup_folder="/home/<USER>/db-backup"
metal_name="metallobaza-$(date '+%Y-%m-%d_%H-%M-%S').sql"
lara_name="lara-$(date '+%Y-%m-%d_%H-%M-%S').sql"

set -e

{
    echo "===== $(date '+%Y-%m-%d %H:%M:%S') Starting job ====="
    echo "Backing up databases..."
    mysqldump --no-tablespaces -ualmishop_sol -p2ykQbWz4rzyA almishop_metallobaza > $backup_folder/$metal_name
    echo "$backup_folder/$metal_name done"
    mysqldump --no-tablespaces -ualmishop_sol -p2ykQbWz4rzyA almishop_lara > $backup_folder/$lara_name
    echo "$backup_folder/$lara_name done"
    echo "Backup finished!"
    echo "Activating python venv..."
    source /home/<USER>/virtualenv/api_server/3.13/bin/activate
    echo "Venv activated"
    echo "Starting sync..."
    python3 /home/<USER>/api_server/almishop/main.py get_full --dryrun
    echo "Sync done."
    echo "Deactivating venv..."
    deactivate
    echo "Venv deactivated"
    echo "===== $(date '+%Y-%m-%d %H:%M:%S') Job finished successfully ====="
} >> "$LOGFILE" 2>&1
