import logging
from typing import List, Dict, Any
from db.adapters import MySQLAdapter, SQLiteAdapter
from utils import (
    API_ENDPOINTS,
    TABLE_NAME,
    SYNC_COLUMN,
    EXTRA_PARAMS,
    NOT_NULLABLE_COLUMNS,
    get_table_columns,
    get_update_sql,
    get_normalized_row,
    get_insert_sql,
    get_existing_sync_values,
    execute_nullify,
    execute_boolify,
    execute_activate,
)


def execute_update(
    adapter: MySQLAdapter | SQLiteAdapter,
    data: List[Dict[str, Any]],
    table: str,
) -> int:
    logging.info(f"Executing update for {len(data)} rows")

    cntr = 0
    valid_columns = get_table_columns(adapter, table)

    if SYNC_COLUMN not in valid_columns:
        logging.error(
            f"Execute update failed: Table {table} must have a {SYNC_COLUMN} column."
        )
        raise ValueError(f"Table must have a {SYNC_COLUMN} column.")

    for row in data:
        if SYNC_COLUMN not in row:
            logging.warning(f"Skipping row without {SYNC_COLUMN}: {row}")
            continue

        row = get_normalized_row(row, valid_columns)

        sync_value = row.pop(SYNC_COLUMN)
        if not sync_value or sync_value == "":
            logging.warning(f"Skipping row with empty {SYNC_COLUMN}: {row}")
            continue

        if not row:
            logging.warning(f"Skipping row without valid columns: {row}")
            continue

        sql = get_update_sql(table, list(row.keys()) + [SYNC_COLUMN])
        values = list(row.values()) + [sync_value]
        adapter.execute(sql, values)
        cntr += 1

    adapter.commit()
    return cntr


def execute_upsert(
    adapter: MySQLAdapter | SQLiteAdapter,
    data: List[Dict],
    table: str,
) -> Dict[str, int]:
    valid_columns = get_table_columns(adapter, table)

    if SYNC_COLUMN not in valid_columns:
        logging.error(
            f"Execute upsert failed: Table {table} must have a {SYNC_COLUMN} column."
        )
        raise ValueError(f"Table must have a {SYNC_COLUMN} column.")

    # Drop rows without vendor and strip unknown fields for each row.
    data = [
        get_normalized_row(row, valid_columns)
        for row in data
        if (SYNC_COLUMN in row) and (row[SYNC_COLUMN] != "")
    ]

    # Build a dict of existing rows keyed by vendor.
    existing = get_existing_sync_values(adapter, table, data)

    #    logging.debug(f"Existing rows: {existing}")

    updates = []
    inserts = []

    # Columns to update except vendor.
    update_cols = [c for c in valid_columns if c != SYNC_COLUMN]

    #    logging.debug(f"update_cols: {update_cols}")

    for row in data:
        sync_value = row[SYNC_COLUMN]
        if sync_value in existing:  # Got vendor, will update
            # Grab old values for every column
            merged = {col: existing[sync_value].get(col) for col in valid_columns}
            #            logging.debug(f"merged: {merged}")

            # Merge new values from dataset with existing. only replace provided values
            # Need to do it for executemany, otherwise it will set everything to NULL
            merged.update(row)
            #            logging.debug(f"row to merge updated: {row}")
            #            logging.debug(f"merged updated: {merged}")
            values = [merged[col] for col in update_cols] + [sync_value]
            #            logging.debug(f"values: {values}")
            updates.append(values)
        else:  # No vendor, will insert
            for col, val in NOT_NULLABLE_COLUMNS.items():
                if row.get(col) is None or row.get(col) == "":
                    row[col] = val

            insert_values = [row.get(col) for col in valid_columns]
            inserts.append(insert_values)

    update_sql = get_update_sql(table, update_cols + [SYNC_COLUMN])
    insert_sql = get_insert_sql(table, valid_columns)

    # Run in bulk
    if updates:
        logging.info(f"Executing update for {len(updates)} rows")
        adapter.executemany(update_sql, updates)
        adapter.commit()

    if inserts:
        logging.info(f"Executing insert for {len(inserts)} rows")
        adapter.executemany(insert_sql, inserts)
        adapter.commit()

    return {"updates": len(updates), "inserts": len(inserts)}


def sync_site(site_key, rows, mode, adapter):
    extras = EXTRA_PARAMS.get(site_key, {})
    nullify = extras.get("nullify", False)
    boolify = extras.get("boolify", False)
    activate = extras.get("activate", False)

    logging.info(f"Syncing {site_key}")
    if mode == API_ENDPOINTS["changes"]:
        count = execute_update(adapter, rows, TABLE_NAME)
        logging.info(f"Updated {count} rows for site {site_key}")
    elif mode == API_ENDPOINTS["full"]:
        if nullify:
            logging.info(f"Executing pre-upsert operations for site {site_key}")
            execute_nullify(adapter, TABLE_NAME)

        logging.info(f"Executing upsert for site {site_key}")
        count = execute_upsert(adapter, rows, TABLE_NAME)

        total = count["updates"] + count["inserts"]
        logging.info(
            f"Upserted {total} rows ({count['updates']} updates, {count['inserts']} inserts) for site {site_key}"
        )

        if boolify or activate:
            logging.info(f"Executing post-upsert operations for site {site_key}")

        if boolify:
            execute_boolify(adapter, TABLE_NAME)

        if activate:
            execute_activate(adapter, TABLE_NAME)

        logging.info(f"Syncing {site_key} finished")
