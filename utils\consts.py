import os
from dotenv import load_dotenv

load_dotenv()

API_BASE_URL = os.getenv("API_BASE_URL")
API_TIMEOUT = float(os.getenv("API_TIMEOUT_SECONDS", 30))
API_ENDPOINTS = {
    "full": os.getenv("API_EP_FULL"),
    "changes": os.getenv("API_EP_CHANGES"),
}

LARA_DB_URL = os.getenv("ALMISHOP_LARA_DB_URL")
METALLOBAZA_DB_URL = os.getenv("ALMISHOP_METALLOBAZA_DB_URL")


DB_SITE_KEYS = {
    "lara": "almishop.by",
    "metallobaza": "metallobaza.by",
}

DB_MAP = {
    DB_SITE_KEYS["lara"]: LARA_DB_URL,
    DB_SITE_KEYS["metallobaza"]: METALLOBAZA_DB_URL,
}

TABLE_NAME = "products"
SYNC_COLUMN = "vendor"
IS_ACTIVE_COLUMN = "is_active"
STOCK_COLUMN = "in_stock"

# nullify - set in_stock and is_active to 0 for all existing rows before upsert
# activate - set is_active to 1 for all rows where in_stock > 0
# boolify - set in_stock to 1 if > 0, 0 if 0 for all rows
EXTRA_PARAMS = {
    DB_SITE_KEYS["metallobaza"]: {"activate": True, "nullify": True, "boolify": True},
    DB_SITE_KEYS["lara"]: {"boolify": True},
    "test": {"activate": True, "boolify": True, "nullify": True},
}

DRYRUN_DIR = os.getenv("DRYRUN_DIR", "dryruns")
SQL_LOGS_DIR = os.getenv("SQL_LOGS_DIR", "sql_logs")
